#!/bin/bash

# =============================================================================
# Kritrima AI CLI - WSL Automatic Installation Script
# =============================================================================
# This script automatically installs and configures Kritrima AI CLI in WSL
# Compatible with Ubuntu, Debian, and other Debian-based WSL distributions
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Configuration
MIN_NODE_VERSION="18.0.0"
RECOMMENDED_NODE_VERSION="20"
PROJECT_NAME="kritrima-ai"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# =============================================================================
# Utility Functions
# =============================================================================

print_header() {
    echo -e "${PURPLE}================================================================================================${NC}"
    echo -e "${WHITE}                           🚀 Kritrima AI CLI - WSL Installation Script${NC}"
    echo -e "${PURPLE}================================================================================================${NC}"
    echo -e "${CYAN}Platform: $(uname -s) $(uname -r)${NC}"
    echo -e "${CYAN}Architecture: $(uname -m)${NC}"
    echo -e "${CYAN}Distribution: $(lsb_release -d 2>/dev/null | cut -f2 || echo "Unknown")${NC}"
    echo -e "${PURPLE}================================================================================================${NC}"
    echo
}

print_step() {
    echo -e "${BLUE}📋 Step $1: $2${NC}"
    echo -e "${YELLOW}$3${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

confirm_action() {
    local message="$1"
    local default="${2:-y}"
    
    if [[ "$AUTO_YES" == "true" ]]; then
        echo -e "${CYAN}$message [Auto-confirmed]${NC}"
        return 0
    fi
    
    while true; do
        if [[ "$default" == "y" ]]; then
            read -p "$(echo -e "${CYAN}$message [Y/n]: ${NC}")" yn
            yn=${yn:-y}
        else
            read -p "$(echo -e "${CYAN}$message [y/N]: ${NC}")" yn
            yn=${yn:-n}
        fi
        
        case $yn in
            [Yy]* ) return 0;;
            [Nn]* ) return 1;;
            * ) echo "Please answer yes or no.";;
        esac
    done
}

version_compare() {
    printf '%s\n%s\n' "$1" "$2" | sort -V | head -n1
}

check_command() {
    command -v "$1" >/dev/null 2>&1
}

# =============================================================================
# System Checks and Prerequisites
# =============================================================================

check_wsl_environment() {
    print_step "1" "Checking WSL Environment" "Verifying that we're running in WSL and checking system compatibility..."
    
    # Check if running in WSL
    if ! grep -qi microsoft /proc/version 2>/dev/null && ! grep -qi wsl /proc/version 2>/dev/null; then
        print_warning "This script is designed for WSL. Detected environment may not be WSL."
        if ! confirm_action "Continue anyway?"; then
            exit 1
        fi
    else
        print_success "WSL environment detected"
    fi
    
    # Check distribution
    if check_command lsb_release; then
        DISTRO=$(lsb_release -si)
        VERSION=$(lsb_release -sr)
        print_info "Distribution: $DISTRO $VERSION"
        
        case "$DISTRO" in
            Ubuntu|Debian|"Linux Mint")
                print_success "Supported distribution detected"
                ;;
            *)
                print_warning "Untested distribution. This script is optimized for Ubuntu/Debian."
                if ! confirm_action "Continue anyway?"; then
                    exit 1
                fi
                ;;
        esac
    fi
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This may cause permission issues."
        if ! confirm_action "Continue as root?"; then
            exit 1
        fi
        IS_ROOT=true
    else
        IS_ROOT=false
    fi
    
    # Check sudo access
    if ! $IS_ROOT && ! sudo -n true 2>/dev/null; then
        print_info "This script requires sudo access for some operations."
        echo "Testing sudo access..."
        if ! sudo true; then
            print_error "Cannot obtain sudo access. Please run with sudo or ensure your user has sudo privileges."
            exit 1
        fi
    fi
    
    print_success "WSL environment check completed"
    echo
}

update_system_packages() {
    print_step "2" "Updating System Packages" "Ensuring system packages are up to date..."
    
    if confirm_action "Update system packages? (Recommended)"; then
        print_info "Updating package lists..."
        if $IS_ROOT; then
            apt update
        else
            sudo apt update
        fi
        
        print_info "Installing essential build tools..."
        if $IS_ROOT; then
            apt install -y curl wget gnupg2 software-properties-common build-essential git
        else
            sudo apt install -y curl wget gnupg2 software-properties-common build-essential git
        fi
        
        print_success "System packages updated"
    else
        print_warning "Skipping system package update"
    fi
    echo
}

# =============================================================================
# Node.js Installation and Management
# =============================================================================

check_nodejs() {
    print_step "3" "Checking Node.js Installation" "Verifying Node.js version and npm availability..."
    
    if check_command node && check_command npm; then
        CURRENT_NODE_VERSION=$(node --version | sed 's/v//')
        NPM_VERSION=$(npm --version)
        
        print_info "Current Node.js version: $CURRENT_NODE_VERSION"
        print_info "Current npm version: $NPM_VERSION"
        
        if [[ "$(version_compare "$CURRENT_NODE_VERSION" "$MIN_NODE_VERSION")" == "$MIN_NODE_VERSION" ]]; then
            print_success "Node.js version meets requirements (>= $MIN_NODE_VERSION)"
            NODE_NEEDS_UPDATE=false
        else
            print_warning "Node.js version $CURRENT_NODE_VERSION is below minimum required version $MIN_NODE_VERSION"
            NODE_NEEDS_UPDATE=true
        fi
    else
        print_warning "Node.js or npm not found"
        NODE_NEEDS_UPDATE=true
    fi
    
    echo
}

install_nodejs() {
    if [[ "$NODE_NEEDS_UPDATE" == "true" ]]; then
        print_step "4" "Installing/Updating Node.js" "Installing Node.js $RECOMMENDED_NODE_VERSION LTS..."
        
        # Offer installation methods
        echo "Choose Node.js installation method:"
        echo "1) NodeSource repository (recommended for WSL)"
        echo "2) Node Version Manager (nvm)"
        echo "3) Skip Node.js installation"
        
        if [[ "$AUTO_YES" == "true" ]]; then
            choice="1"
            echo "Auto-selected: NodeSource repository"
        else
            read -p "Enter choice [1-3]: " choice
        fi
        
        case $choice in
            1)
                install_nodejs_nodesource
                ;;
            2)
                install_nodejs_nvm
                ;;
            3)
                print_warning "Skipping Node.js installation. Please ensure Node.js >= $MIN_NODE_VERSION is installed."
                ;;
            *)
                print_error "Invalid choice. Defaulting to NodeSource repository."
                install_nodejs_nodesource
                ;;
        esac
    else
        print_info "Node.js installation check skipped (version is adequate)"
    fi
    echo
}

install_nodejs_nodesource() {
    print_info "Installing Node.js via NodeSource repository..."
    
    # Remove existing Node.js installations that might conflict
    if confirm_action "Remove existing Node.js packages to avoid conflicts?"; then
        if $IS_ROOT; then
            apt remove -y nodejs npm 2>/dev/null || true
        else
            sudo apt remove -y nodejs npm 2>/dev/null || true
        fi
    fi
    
    # Add NodeSource repository
    print_info "Adding NodeSource repository..."
    curl -fsSL https://deb.nodesource.com/setup_${RECOMMENDED_NODE_VERSION}.x | if $IS_ROOT; then bash -; else sudo -E bash -; fi
    
    # Install Node.js
    print_info "Installing Node.js..."
    if $IS_ROOT; then
        apt install -y nodejs
    else
        sudo apt install -y nodejs
    fi
    
    # Verify installation
    if check_command node && check_command npm; then
        NEW_NODE_VERSION=$(node --version)
        NEW_NPM_VERSION=$(npm --version)
        print_success "Node.js $NEW_NODE_VERSION and npm $NEW_NPM_VERSION installed successfully"
    else
        print_error "Node.js installation failed"
        exit 1
    fi
}

install_nodejs_nvm() {
    print_info "Installing Node.js via Node Version Manager (nvm)..."

    # Install nvm
    print_info "Installing nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

    # Source nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

    # Install latest LTS Node.js
    print_info "Installing Node.js LTS..."
    nvm install --lts
    nvm use --lts
    nvm alias default lts/*

    # Verify installation
    if check_command node && check_command npm; then
        NEW_NODE_VERSION=$(node --version)
        NEW_NPM_VERSION=$(npm --version)
        print_success "Node.js $NEW_NODE_VERSION and npm $NEW_NPM_VERSION installed successfully via nvm"

        # Add nvm to shell profile
        if ! grep -q "NVM_DIR" ~/.bashrc; then
            echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.bashrc
            echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> ~/.bashrc
            echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> ~/.bashrc
            print_info "Added nvm configuration to ~/.bashrc"
        fi
    else
        print_error "Node.js installation via nvm failed"
        exit 1
    fi
}

# =============================================================================
# Project Setup and Installation
# =============================================================================

setup_project_directory() {
    print_step "5" "Setting Up Project Directory" "Configuring optimal project location for WSL performance..."

    # Determine current project location
    CURRENT_DIR="$SCRIPT_DIR"
    print_info "Current project location: $CURRENT_DIR"

    # Check if we're in Windows file system (/mnt/c)
    if [[ "$CURRENT_DIR" == /mnt/c/* ]]; then
        print_warning "Project is currently in Windows file system (/mnt/c/)"
        print_info "For better performance, it's recommended to copy the project to WSL file system"

        if confirm_action "Copy project to WSL home directory for better performance?"; then
            WSL_PROJECT_DIR="$HOME/kritrima-ai-cli"

            if [[ -d "$WSL_PROJECT_DIR" ]]; then
                if confirm_action "Directory $WSL_PROJECT_DIR already exists. Remove it?"; then
                    rm -rf "$WSL_PROJECT_DIR"
                else
                    WSL_PROJECT_DIR="$HOME/kritrima-ai-cli-$(date +%s)"
                    print_info "Using alternative directory: $WSL_PROJECT_DIR"
                fi
            fi

            print_info "Copying project to $WSL_PROJECT_DIR..."
            cp -r "$CURRENT_DIR" "$WSL_PROJECT_DIR"
            cd "$WSL_PROJECT_DIR"
            PROJECT_DIR="$WSL_PROJECT_DIR"
            print_success "Project copied to WSL file system"
        else
            PROJECT_DIR="$CURRENT_DIR"
            cd "$PROJECT_DIR"
        fi
    else
        print_success "Project is already in WSL file system"
        PROJECT_DIR="$CURRENT_DIR"
        cd "$PROJECT_DIR"
    fi

    print_info "Working directory: $PROJECT_DIR"
    echo
}

install_project_dependencies() {
    print_step "6" "Installing Project Dependencies" "Installing npm packages and building the project..."

    # Verify we're in the right directory
    if [[ ! -f "package.json" ]]; then
        print_error "package.json not found in current directory: $(pwd)"
        print_error "Please ensure you're running this script from the project root directory"
        exit 1
    fi

    # Clean existing installations if requested
    if [[ -d "node_modules" ]] || [[ -f "package-lock.json" ]]; then
        if confirm_action "Clean existing node_modules and package-lock.json?"; then
            print_info "Cleaning existing installation..."
            rm -rf node_modules package-lock.json
            print_success "Cleaned existing installation"
        fi
    fi

    # Configure npm for better WSL performance
    print_info "Configuring npm for WSL..."
    npm config set fund false
    npm config set audit false
    npm config set progress false

    # Install dependencies
    print_info "Installing project dependencies..."
    if ! npm install; then
        print_error "npm install failed"
        print_info "Trying with --no-optional flag..."
        if ! npm install --no-optional; then
            print_error "npm install failed even with --no-optional"
            exit 1
        fi
    fi

    print_success "Dependencies installed successfully"
    echo
}

build_project() {
    print_step "7" "Building Project" "Compiling TypeScript and preparing distribution files..."

    # Run the build process
    print_info "Building project..."
    if ! npm run build; then
        print_error "Build failed"
        print_info "Checking for TypeScript compilation errors..."

        # Try to get more detailed error information
        if check_command npx; then
            print_info "Running TypeScript compiler directly..."
            npx tsc --noEmit
        fi
        exit 1
    fi

    # Verify build output
    if [[ -d "dist" ]] && [[ -f "dist/cli.js" ]]; then
        print_success "Project built successfully"
        print_info "Build output available in dist/ directory"
    else
        print_error "Build completed but expected output files not found"
        exit 1
    fi

    # Fix permissions for the binary
    if [[ -f "bin/kritrima-ai.js" ]]; then
        chmod +x bin/kritrima-ai.js
        print_success "Binary permissions fixed"
    fi

    echo
}

# =============================================================================
# Environment Configuration
# =============================================================================

setup_environment_variables() {
    print_step "8" "Setting Up Environment Variables" "Configuring API keys and environment settings..."

    # Check for existing .env file
    if [[ -f ".env" ]]; then
        print_info "Found existing .env file"
        if confirm_action "Keep existing .env file?"; then
            print_success "Keeping existing .env file"
            return
        fi
    fi

    # Create .env file
    print_info "Creating .env file..."
    cat > .env << 'EOF'
# Kritrima AI CLI Environment Configuration
# ===========================================

# OpenAI Configuration (Primary)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Configuration (Alternative)
# Get your API key from: https://console.anthropic.com/
# ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Kritrima AI Configuration
KRITRIMA_PROVIDER=openai
KRITRIMA_MODEL=gpt-4
KRITRIMA_APPROVAL_MODE=suggest
KRITRIMA_ENABLE_TELEMETRY=false
KRITRIMA_ENABLE_LOGGING=true
KRITRIMA_ENABLE_NOTIFICATIONS=true

# WSL-Specific Configuration
KRITRIMA_WSL_MODE=true
EOF

    print_success ".env file created"
    print_warning "⚠️  IMPORTANT: You need to edit .env and add your actual API keys!"
    print_info "Edit the .env file with: nano .env"

    # Add environment variables to shell profile
    if confirm_action "Add environment variables to ~/.bashrc for persistence?"; then
        if ! grep -q "# Kritrima AI CLI Environment" ~/.bashrc; then
            cat >> ~/.bashrc << 'EOF'

# Kritrima AI CLI Environment
export KRITRIMA_PROVIDER=openai
export KRITRIMA_MODEL=gpt-4
export KRITRIMA_APPROVAL_MODE=suggest
export KRITRIMA_ENABLE_TELEMETRY=false
EOF
            print_success "Environment variables added to ~/.bashrc"
        else
            print_info "Environment variables already exist in ~/.bashrc"
        fi
    fi

    echo
}

create_config_file() {
    print_step "9" "Creating Configuration File" "Setting up Kritrima AI configuration..."

    # Create config directory if it doesn't exist
    mkdir -p ~/.config/kritrima-ai

    # Create global config file
    CONFIG_FILE="$HOME/.kritrima.json"

    if [[ -f "$CONFIG_FILE" ]]; then
        if ! confirm_action "Configuration file already exists. Overwrite it?"; then
            print_info "Keeping existing configuration file"
            return
        fi
    fi

    print_info "Creating configuration file: $CONFIG_FILE"
    cat > "$CONFIG_FILE" << 'EOF'
{
  "provider": "openai",
  "model": "gpt-4",
  "approvalMode": "suggest",
  "enableLogging": true,
  "enableNotifications": true,
  "enableTelemetry": false,
  "safeCommands": [
    "ls", "cat", "echo", "pwd", "whoami", "date", "uname",
    "git status", "git log", "git diff", "git branch"
  ],
  "additionalWritableRoots": [
    "./temp",
    "./output",
    "./logs"
  ],
  "wslMode": true,
  "maxFileSize": "10MB",
  "maxExecutionTime": 30000,
  "autoSave": true,
  "theme": "dark"
}
EOF

    print_success "Configuration file created: $CONFIG_FILE"
    echo
}

# =============================================================================
# Global Installation and Testing
# =============================================================================

install_globally() {
    print_step "10" "Installing Globally" "Making Kritrima AI CLI available system-wide..."

    # Test local installation first
    print_info "Testing local installation..."
    if ! npm run test:build; then
        print_error "Local installation test failed"
        exit 1
    fi

    print_success "Local installation test passed"

    # Choose installation method
    echo "Choose global installation method:"
    echo "1) Install with sudo (recommended)"
    echo "2) Install to user directory (no sudo required)"
    echo "3) Use project's install fix script"
    echo "4) Skip global installation"

    if [[ "$AUTO_YES" == "true" ]]; then
        choice="1"
        echo "Auto-selected: Install with sudo"
    else
        read -p "Enter choice [1-4]: " choice
    fi

    case $choice in
        1)
            install_global_sudo
            ;;
        2)
            install_global_user
            ;;
        3)
            install_global_fix_script
            ;;
        4)
            print_warning "Skipping global installation"
            print_info "You can run the CLI locally with: npm run dev"
            ;;
        *)
            print_error "Invalid choice. Defaulting to sudo installation."
            install_global_sudo
            ;;
    esac

    echo
}

install_global_sudo() {
    print_info "Installing globally with sudo..."

    # Clean any existing global installation
    if confirm_action "Clean any existing global installation?"; then
        sudo npm uninstall -g kritrima-ai 2>/dev/null || true
        sudo rm -f /usr/bin/kritrima-ai /usr/local/bin/kritrima-ai 2>/dev/null || true
    fi

    # Install globally
    if sudo npm install -g .; then
        print_success "Global installation completed"
    else
        print_error "Global installation failed"
        print_info "Trying with --force flag..."
        if sudo npm install -g . --force; then
            print_success "Global installation completed with --force"
        else
            print_error "Global installation failed even with --force"
            return 1
        fi
    fi
}

install_global_user() {
    print_info "Installing to user directory..."

    # Configure npm for user installation
    npm config set prefix ~/.local

    # Add to PATH if not already there
    if ! echo "$PATH" | grep -q "$HOME/.local/bin"; then
        export PATH="$HOME/.local/bin:$PATH"
        if ! grep -q 'export PATH="$HOME/.local/bin:$PATH"' ~/.bashrc; then
            echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
            print_info "Added ~/.local/bin to PATH in ~/.bashrc"
        fi
    fi

    # Install globally to user directory
    if npm install -g .; then
        print_success "User-local installation completed"
    else
        print_error "User-local installation failed"
        return 1
    fi
}

install_global_fix_script() {
    print_info "Using project's install fix script..."

    if [[ -f "scripts/install-fix.js" ]]; then
        node scripts/install-fix.js --auto-install
    else
        print_error "Install fix script not found"
        return 1
    fi
}

# =============================================================================
# Testing and Verification
# =============================================================================

test_installation() {
    print_step "11" "Testing Installation" "Verifying that everything works correctly..."

    # Test if kritrima-ai command is available
    print_info "Testing kritrima-ai command availability..."
    if check_command kritrima-ai; then
        print_success "kritrima-ai command found in PATH"

        # Test version
        print_info "Testing version command..."
        if kritrima-ai --version; then
            print_success "Version command works"
        else
            print_error "Version command failed"
        fi

        # Test help
        print_info "Testing help command..."
        if kritrima-ai --help >/dev/null 2>&1; then
            print_success "Help command works"
        else
            print_error "Help command failed"
        fi

        # Test basic functionality (if API key is configured)
        if [[ -f ".env" ]] && grep -q "OPENAI_API_KEY=sk-" .env 2>/dev/null; then
            print_info "Testing basic functionality with API..."
            if kritrima-ai "echo hello world" --single-pass >/dev/null 2>&1; then
                print_success "Basic functionality test passed"
            else
                print_warning "Basic functionality test failed (this is normal without a valid API key)"
            fi
        else
            print_info "Skipping API test (no valid API key configured)"
        fi

    else
        print_error "kritrima-ai command not found in PATH"
        print_info "You may need to restart your terminal or run: source ~/.bashrc"

        # Try to find the binary
        print_info "Searching for kritrima-ai binary..."
        find /usr -name "kritrima-ai" 2>/dev/null || true
        find ~/.local -name "kritrima-ai" 2>/dev/null || true
        find ~/.npm-global -name "kritrima-ai" 2>/dev/null || true
    fi

    echo
}

run_integration_tests() {
    print_step "12" "Running Integration Tests" "Executing project test suite..."

    if confirm_action "Run the project's test suite?"; then
        print_info "Running npm test..."
        if npm test; then
            print_success "All tests passed"
        else
            print_warning "Some tests failed (this may be normal for integration tests without API keys)"
        fi
    else
        print_info "Skipping test suite"
    fi

    echo
}

# =============================================================================
# Final Setup and Instructions
# =============================================================================

final_setup() {
    print_step "13" "Final Setup" "Completing installation and providing usage instructions..."

    # Create useful directories
    mkdir -p ~/kritrima-ai-workspace/{temp,output,logs}
    print_info "Created workspace directories in ~/kritrima-ai-workspace/"

    # Create a quick start script
    cat > ~/kritrima-ai-quickstart.sh << 'EOF'
#!/bin/bash
# Kritrima AI CLI Quick Start Script

echo "🚀 Kritrima AI CLI Quick Start"
echo "=============================="
echo

# Check if API key is configured
if [[ -z "$OPENAI_API_KEY" ]] && [[ -z "$ANTHROPIC_API_KEY" ]]; then
    echo "⚠️  No API key configured!"
    echo "Please set your API key:"
    echo "export OPENAI_API_KEY='your-key-here'"
    echo "or"
    echo "export ANTHROPIC_API_KEY='your-key-here'"
    echo
fi

echo "Available commands:"
echo "  kritrima-ai --help                    # Show help"
echo "  kritrima-ai --version                 # Show version"
echo "  kritrima-ai                           # Start interactive mode"
echo "  kritrima-ai 'your question' --single-pass  # Single question mode"
echo
echo "Configuration files:"
echo "  ~/.kritrima.json                      # Global configuration"
echo "  .env                                  # Environment variables"
echo
echo "For more information, visit: https://github.com/kritrima-ai/kritrima-ai-cli"
EOF

    chmod +x ~/kritrima-ai-quickstart.sh
    print_success "Created quick start script: ~/kritrima-ai-quickstart.sh"

    echo
}

print_final_instructions() {
    print_header
    echo -e "${GREEN}🎉 Installation Complete!${NC}"
    echo -e "${PURPLE}================================================================================================${NC}"
    echo

    echo -e "${CYAN}📋 Next Steps:${NC}"
    echo -e "${WHITE}1. Configure your API key:${NC}"
    echo -e "   ${YELLOW}nano .env${NC}  # Edit the .env file and add your OpenAI or Anthropic API key"
    echo

    echo -e "${WHITE}2. Test the installation:${NC}"
    echo -e "   ${YELLOW}kritrima-ai --version${NC}"
    echo -e "   ${YELLOW}kritrima-ai --help${NC}"
    echo

    echo -e "${WHITE}3. Start using Kritrima AI:${NC}"
    echo -e "   ${YELLOW}kritrima-ai${NC}  # Interactive mode"
    echo -e "   ${YELLOW}kritrima-ai 'help me with this code' --single-pass${NC}  # Single question"
    echo

    echo -e "${CYAN}📁 Important Files:${NC}"
    echo -e "   ${WHITE}Project directory:${NC} $PROJECT_DIR"
    echo -e "   ${WHITE}Configuration:${NC} ~/.kritrima.json"
    echo -e "   ${WHITE}Environment:${NC} .env"
    echo -e "   ${WHITE}Quick start:${NC} ~/kritrima-ai-quickstart.sh"
    echo

    echo -e "${CYAN}🔧 Troubleshooting:${NC}"
    echo -e "   ${WHITE}If command not found:${NC} source ~/.bashrc"
    echo -e "   ${WHITE}Check installation:${NC} which kritrima-ai"
    echo -e "   ${WHITE}View logs:${NC} npm run dev"
    echo -e "   ${WHITE}Reinstall:${NC} ./install-wsl.sh"
    echo

    echo -e "${CYAN}📚 Documentation:${NC}"
    echo -e "   ${WHITE}GitHub:${NC} https://github.com/kritrima-ai/kritrima-ai-cli"
    echo -e "   ${WHITE}README:${NC} cat README.md"
    echo -e "   ${WHITE}Quick start:${NC} ~/kritrima-ai-quickstart.sh"
    echo

    echo -e "${PURPLE}================================================================================================${NC}"
    echo -e "${GREEN}✅ Kritrima AI CLI is ready to use! Happy coding! 🚀${NC}"
    echo -e "${PURPLE}================================================================================================${NC}"
}

# =============================================================================
# Main Execution Flow
# =============================================================================

main() {
    # Parse command line arguments
    AUTO_YES=false
    SKIP_TESTS=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -y|--yes|--auto)
                AUTO_YES=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -h|--help)
                echo "Kritrima AI CLI - WSL Installation Script"
                echo
                echo "Usage: $0 [OPTIONS]"
                echo
                echo "Options:"
                echo "  -y, --yes, --auto    Auto-confirm all prompts"
                echo "  --skip-tests         Skip running tests"
                echo "  -h, --help           Show this help message"
                echo
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done

    # Main installation flow
    print_header

    # System checks and prerequisites
    check_wsl_environment
    update_system_packages

    # Node.js setup
    check_nodejs
    install_nodejs

    # Project setup
    setup_project_directory
    install_project_dependencies
    build_project

    # Configuration
    setup_environment_variables
    create_config_file

    # Installation and testing
    install_globally
    test_installation

    if [[ "$SKIP_TESTS" != "true" ]]; then
        run_integration_tests
    fi

    # Final setup
    final_setup
    print_final_instructions
}

# Run main function with all arguments
main "$@"
